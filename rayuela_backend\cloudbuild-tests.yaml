steps:
  # 1. Setup and Test Environment Validation
  - name: 'python:3.12-slim'
    id: 'validate-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔍 Validando entorno de tests..."
        cd rayuela_backend
        python --version
        echo "✅ Python version validated"
        
        # Verificar que existe el directorio de tests
        if [ ! -d "tests" ]; then
          echo "❌ Directorio de tests no encontrado"
          exit 1
        fi
        echo "✅ Test directory found"
        
        # Validar archivos de configuración críticos
        for file in "requirements.txt" "requirements-dev.txt" "pytest.ini" ".coveragerc"; do
          if [ ! -f "$file" ]; then
            echo "❌ Archivo de configuración faltante: $file"
            exit 1
          fi
        done
        echo "✅ Configuration files validated"

  # 2. Install Dependencies and Run Health Check
  - name: 'python:3.12-slim'
    id: 'install-dependencies'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "📦 Instalando dependencias..."
        cd rayuela_backend
        pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
        # Ejecutar diagnóstico de tests
        echo "🔍 Ejecutando diagnóstico de tests..."
        python scripts/test_health_checker.py
        echo "✅ Test health check completed"

  # 3. Code Quality and Security Checks
  - name: 'python:3.12-slim'
    id: 'quality-and-security'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔍 Ejecutando verificaciones de calidad y seguridad..."
        cd rayuela_backend
        pip install -r requirements-dev.txt
        pip install bandit safety
        
        # Linting
        echo "📝 Revisando formato de código..."
        black --check src/ tests/ || echo "⚠️ Black encontró problemas de formato"
        isort --check-only src/ tests/ || echo "⚠️ isort encontró problemas de orden de imports"
        flake8 src/ || echo "⚠️ Flake8 encontró problemas de estilo"
        
        # Security scan
        echo "🔒 Escaneando vulnerabilidades de seguridad..."
        bandit -r src/ -ll || echo "⚠️ Bandit encontró problemas de seguridad"
        safety check || echo "⚠️ Safety encontró vulnerabilidades en dependencias"
        
        echo "✅ Quality and security checks completed"

  # 4. Run Unit Tests with Coverage
  - name: 'python:3.12-slim'
    id: 'unit-tests'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🧪 Ejecutando tests unitarios..."
        cd rayuela_backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
        # Ejecutar tests unitarios con cobertura
        python -m pytest tests/unit/ \
          -v \
          --tb=short \
          --junitxml=unit_test_results.xml \
          --cov=src \
          --cov-report=xml:coverage_unit.xml \
          --cov-report=term \
          --cov-fail-under=70 \
          || (echo "❌ Unit tests failed" && exit 1)
        
        echo "✅ Unit tests completed successfully"

  # 5. Build Test Environment with Docker
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-test-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🐳 Construyendo entorno de tests..."
        cd rayuela_backend
        
        # Construir imagen de test
        docker build -f Dockerfile.test -t rayuela-test:$COMMIT_SHA .
        
        echo "✅ Test environment built successfully"

  # 6. Run Integration Tests with Docker Compose
  - name: 'docker/compose:1.29.2'
    id: 'integration-tests'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔗 Ejecutando tests de integración..."
        cd rayuela_backend
        
        # Ejecutar tests de integración
        docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit --exit-code-from test-runner
        
        # Capturar resultados
        exit_code=$?
        
        # Limpiar contenedores
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ Integration tests failed"
          exit $exit_code
        fi
        
        echo "✅ Integration tests completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 7. Run End-to-End Tests
  - name: 'docker/compose:1.29.2'
    id: 'e2e-tests'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🌐 Ejecutando tests end-to-end..."
        cd rayuela_backend
        
        # Configurar y ejecutar tests E2E
        docker-compose -f docker-compose.test.yml run --rm test-runner \
          pytest tests/e2e/ \
          -v \
          --tb=short \
          --junitxml=/app/e2e_test_results.xml
        
        exit_code=$?
        
        # Limpiar
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "⚠️ E2E tests failed - not blocking deployment"
        else
          echo "✅ E2E tests completed successfully"
        fi
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 8. Run Comprehensive Multi-Tenancy Validation
  - name: 'docker/compose:1.29.2'
    id: 'multi-tenancy-validation'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔒 Ejecutando validación comprehensiva de multi-tenancy..."
        cd rayuela_backend
        
        # Ejecutar tests de multi-tenancy comprehensivos
        docker-compose -f docker-compose.test.yml run --rm test-runner \
          pytest tests/integration/test_multi_tenancy_comprehensive.py \
          tests/middleware/test_tenant_middleware_comprehensive.py \
          tests/unit/db/repositories/test_base_repository_tenant.py \
          tests/integration/test_celery_tenant_isolation_extended.py \
          -v \
          --tb=short \
          --junitxml=/app/multi_tenancy_test_results.xml \
          --cov=src \
          --cov-report=xml:/app/coverage_multi_tenancy.xml
        
        exit_code=$?
        
        # Limpiar contenedores
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ Multi-tenancy validation tests failed"
          exit $exit_code
        fi
        
        echo "✅ Multi-tenancy validation tests completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 9. Run RLS Policies Verification
  - name: 'python:3.12-slim'
    id: 'rls-verification'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔐 Verificando políticas RLS comprehensivas..."
        cd rayuela_backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
        # Configurar variables de entorno para DB
        export POSTGRES_HOST=postgres
        export POSTGRES_PORT=5432
        export POSTGRES_USER=postgres
        export POSTGRES_PASSWORD=postgres
        export POSTGRES_DB=rayuela_test
        export DATABASE_URL="********************************************/rayuela_test"
        
        # Ejecutar verificación RLS comprehensiva
        python -m scripts.maintenance.verify_rls_comprehensive
        
        exit_code=$?
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ RLS verification failed - CRITICAL SECURITY ISSUE"
          exit $exit_code
        fi
        
        echo "✅ RLS verification completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'ENV=test'

  # 10. Run BaseRepository Isolation Tests
  - name: 'docker/compose:1.29.2'
    id: 'repository-isolation-tests'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🗃️ Ejecutando tests de aislamiento de repositorios..."
        cd rayuela_backend
        
        # Tests específicos para BaseRepository y aislamiento de datos
        docker-compose -f docker-compose.test.yml run --rm test-runner \
          pytest tests/unit/db/repositories/test_base_repository_tenant.py \
          tests/integration/test_rls_comprehensive_security.py \
          -v \
          --tb=short \
          --junitxml=/app/repository_isolation_test_results.xml \
          --cov=src/db/repositories \
          --cov-report=xml:/app/coverage_repository_isolation.xml \
          --cov-fail-under=85
        
        exit_code=$?
        
        # Limpiar contenedores
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ Repository isolation tests failed"
          exit $exit_code
        fi
        
        echo "✅ Repository isolation tests completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 11. Run Tenant Middleware Validation
  - name: 'docker/compose:1.29.2'
    id: 'tenant-middleware-validation'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔧 Ejecutando validación de TenantMiddleware..."
        cd rayuela_backend
        
        # Tests específicos para TenantMiddleware
        docker-compose -f docker-compose.test.yml run --rm test-runner \
          pytest tests/middleware/test_tenant_middleware_comprehensive.py \
          -v \
          --tb=short \
          --junitxml=/app/tenant_middleware_test_results.xml \
          --cov=src/middleware/tenant \
          --cov-report=xml:/app/coverage_tenant_middleware.xml \
          --cov-fail-under=90
        
        exit_code=$?
        
        # Limpiar contenedores
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ Tenant middleware validation failed"
          exit $exit_code
        fi
        
        echo "✅ Tenant middleware validation completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 12. Run Celery Tenant Isolation Extended Tests
  - name: 'docker/compose:1.29.2'
    id: 'celery-tenant-isolation-extended'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "⚡ Ejecutando tests extendidos de aislamiento Celery..."
        cd rayuela_backend
        
        # Tests extendidos para aislamiento de tenants en tareas Celery
        docker-compose -f docker-compose.test.yml run --rm test-runner \
          pytest tests/integration/test_celery_tenant_isolation_extended.py \
          -v \
          --tb=short \
          --junitxml=/app/celery_tenant_isolation_extended_test_results.xml \
          --cov=src/core/celery_app \
          --cov=src/tasks \
          --cov-report=xml:/app/coverage_celery_tenant_isolation.xml
        
        exit_code=$?
        
        # Limpiar contenedores
        docker-compose -f docker-compose.test.yml down -v
        
        if [ $exit_code -ne 0 ]; then
          echo "❌ Celery tenant isolation extended tests failed"
          exit $exit_code
        fi
        
        echo "✅ Celery tenant isolation extended tests completed successfully"
    env:
      - 'POSTGRES_USER=postgres'
      - 'POSTGRES_PASSWORD=postgres'
      - 'POSTGRES_DB=rayuela_test'
      - 'SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars'
      - 'ENV=test'

  # 13. Generate Multi-Tenancy Security Report
  - name: 'python:3.12-slim'
    id: 'multi-tenancy-security-report'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "📊 Generando reporte de seguridad multi-tenancy..."
        cd rayuela_backend
        
        # Crear directorio de reportes si no existe
        mkdir -p test_reports/security
        
        # Recopilar todos los resultados de tests de multi-tenancy
        find . -name "*multi_tenancy*test_results*.xml" -exec cp {} test_reports/security/ \;
        find . -name "*tenant*test_results*.xml" -exec cp {} test_reports/security/ \;
        find . -name "rls_verification_results.json" -exec cp {} test_reports/security/ \;
        
        # Generar reporte final de seguridad multi-tenancy
        cat > test_reports/security/multi_tenancy_security_summary.txt << EOF
        🔒 REPORTE DE SEGURIDAD MULTI-TENANCY
        ====================================
        Timestamp: $(date)
        Commit: $COMMIT_SHA
        Branch: $BRANCH_NAME
        
        VALIDACIONES EJECUTADAS:
        ✅ Tests comprehensivos de aislamiento CRUD
        ✅ Validación de TenantMiddleware
        ✅ Tests de BaseRepository con filtrado tenant
        ✅ Tests extendidos de aislamiento Celery
        ✅ Verificación comprehensiva de políticas RLS
        
        COBERTURA DE SEGURIDAD:
        - Todas las operaciones CRUD en modelos tenant-scoped
        - Aislamiento en todos los repositorios
        - Propagación correcta de contexto de tenant
        - Políticas RLS en todas las tablas relevantes
        - Aislamiento en tareas asíncronas
        
        ESTADO: VALIDACIÓN COMPLETA ✅
        EOF
        
        echo "✅ Multi-tenancy security report generated"

# Store test artifacts including multi-tenancy reports
artifacts:
  objects:
    location: 'gs://${_BUCKET_NAME}/test-artifacts/$COMMIT_SHA'
    paths:
      - 'rayuela_backend/test_reports/*'
      - 'rayuela_backend/test_health_report.json'
      - 'rayuela_backend/test_results_production.json'
      - 'rayuela_backend/rls_verification_results.json'
      - 'rayuela_backend/*multi_tenancy*test_results*.xml'
      - 'rayuela_backend/*tenant*test_results*.xml'

# Options for the build
options:
  machineType: 'E2_STANDARD_4'
  substitutionOption: 'ALLOW_LOOSE'
  dynamicSubstitutions: true
  logging: CLOUD_LOGGING_ONLY

# Timeouts
timeout: '1200s'  # 20 minutes

# Substitutions
substitutions:
  _BUCKET_NAME: 'rayuela-test-artifacts'
  _COVERAGE_THRESHOLD: '70'
  _TEST_TIMEOUT: '600'

# Configure triggers for different branches
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/DB_PASSWORD/versions/latest
      env: 'DB_PASSWORD'
    - versionName: projects/$PROJECT_ID/secrets/SECRET_KEY/versions/latest
      env: 'SECRET_KEY' 